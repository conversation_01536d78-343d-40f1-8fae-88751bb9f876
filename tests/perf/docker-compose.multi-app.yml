# Multi-Container Performance Test Setup
# Uses existing shared database at localhost:15432
# complies binary from source

services:

  # Load Balancer (nginx)
  loadbalancer:
    image: nginx:alpine
    container_name: signalsd-lb-multi
    ports:
      - "8080:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - signals1
      - signals2
      - signals3
      - admin
    networks:
      - multi-app-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health/live"]
      interval: 5s
      timeout: 5s
      retries: 5

  # Signals Container 1
  signals1:
    build:
      context: ../..
      dockerfile_inline: |
        FROM golang:1.24-alpine
        RUN apk add --no-cache bash postgresql-client curl jq vim git
        WORKDIR /signalsd
        CMD ["/bin/bash"]
    container_name: signalsd-signals1-multi
    volumes:
      - ../..:/signalsd
      - go-modules-multi:/go/pkg/mod
    environment:
      - HOST
      - PORT
      - ENVIRONMENT=perf
      - LOG_LEVEL
      - READ_TIMEOUT
      - WRITE_TIMEOUT
      - IDLE_TIMEOUT
      - RATE_LIMIT_BURST
      - MAX_SIGNAL_PAYLOAD_SIZE
      - MAX_API_REQUEST_SIZE
      - ALLOWED_ORIGINS
      - SECRET_KEY=${SECRET_KEY:-multi-container-test-secret-key-12345}
      - DATABASE_URL=${DATABASE_URL:-postgres://signalsd-dev:@host.docker.internal:15432/signalsd_admin?sslmode=disable}
      - RATE_LIMIT_RPS=0
      - APP_INSTANCE=signals1
      - READ_TIMEOUT=300s
      - WRITE_TIMEOUT=300s
      - IDLE_TIMEOUT=300s
    working_dir: /signalsd
    ports:
      - "8082:8080"  # Expose for direct testing
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health/live"]
      interval: 5s
      timeout: 5s
      retries: 5
    command: >
      sh -c "cd /signalsd/app && go run cmd/signalsd/main.go -m signals"
    networks:
      - multi-app-network
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 512M

  # Signals Container 2
  signals2:
    build:
      context: ../..
      dockerfile_inline: |
        FROM golang:1.24-alpine
        WORKDIR /signalsd
        CMD ["/bin/bash"]
    container_name: signalsd-signals2-multi
    volumes:
      - ../..:/signalsd
      - go-modules-multi:/go/pkg/mod
    environment:
      - HOST
      - PORT
      - ENVIRONMENT=perf
      - LOG_LEVEL
      - READ_TIMEOUT
      - WRITE_TIMEOUT
      - IDLE_TIMEOUT
      - RATE_LIMIT_BURST
      - MAX_SIGNAL_PAYLOAD_SIZE
      - MAX_API_REQUEST_SIZE
      - ALLOWED_ORIGINS
      - SECRET_KEY=${SECRET_KEY:-multi-container-test-secret-key-12345}
      - DATABASE_URL=${DATABASE_URL:-postgres://signalsd-dev:@host.docker.internal:15432/signalsd_admin?sslmode=disable}
      - RATE_LIMIT_RPS=0
      - APP_INSTANCE=signals2
      - READ_TIMEOUT=300s
      - WRITE_TIMEOUT=300s
      - IDLE_TIMEOUT=300s
    working_dir: /signalsd
    ports:
      - "8083:8080"  # Expose for direct testing
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health/live"]
      interval: 5s
      timeout: 5s
      retries: 5
    command: >
      sh -c "cd /signalsd/app && go run cmd/signalsd/main.go -m signals"
    networks:
      - multi-app-network
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 512M

  # Signals Container 3
  signals3:
    build:
      context: ../..
      dockerfile_inline: |
        FROM golang:1.24-alpine
        WORKDIR /signalsd
        CMD ["/bin/bash"]
    container_name: signalsd-signals3-multi
    volumes:
      - ../..:/signalsd
      - go-modules-multi:/go/pkg/mod
    environment:
      - HOST
      - PORT
      - ENVIRONMENT=perf
      - LOG_LEVEL
      - READ_TIMEOUT
      - WRITE_TIMEOUT
      - IDLE_TIMEOUT
      - RATE_LIMIT_BURST
      - MAX_SIGNAL_PAYLOAD_SIZE
      - MAX_API_REQUEST_SIZE
      - ALLOWED_ORIGINS
      - SECRET_KEY=${SECRET_KEY:-multi-container-test-secret-key-12345}
      - DATABASE_URL=${DATABASE_URL:-postgres://signalsd-dev:@host.docker.internal:15432/signalsd_admin?sslmode=disable}
      - RATE_LIMIT_RPS=0
      - APP_INSTANCE=signals3
      - READ_TIMEOUT=300s
      - WRITE_TIMEOUT=300s
      - IDLE_TIMEOUT=300s
    working_dir: /signalsd
    ports:
      - "8084:8080"  # Expose for direct testing
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health/live"]
      interval: 5s
      timeout: 5s
      retries: 5
    command: >
      sh -c "cd /signalsd/app && go run cmd/signalsd/main.go -m signals"
    networks:
      - multi-app-network
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 512M

  # Admin Container
  admin:
    build:
      context: ../..
      dockerfile_inline: |
        FROM golang:1.24-alpine
        # Install system dependencies in one layer for better caching
        RUN apk add --no-cache bash postgresql-client curl jq vim git
        # Install Go tools
        RUN go install github.com/pressly/goose/v3/cmd/goose@latest && \
            go install github.com/sqlc-dev/sqlc/cmd/sqlc@latest && \
            go install github.com/swaggo/swag/cmd/swag@latest
        # Pre-download common Go modules for faster startup
        WORKDIR /tmp/app
        COPY app/go.mod app/go.sum ./
        RUN go mod download
        WORKDIR /signalsd
    container_name: signalsd-admin-multi
    volumes:
      - ../..:/signalsd
      - go-modules-multi:/go/pkg/mod
    environment:
      - HOST
      - PORT
      - ENVIRONMENT=perf
      - LOG_LEVEL
      - READ_TIMEOUT
      - WRITE_TIMEOUT
      - IDLE_TIMEOUT
      - RATE_LIMIT_BURST
      - MAX_SIGNAL_PAYLOAD_SIZE
      - MAX_API_REQUEST_SIZE
      - ALLOWED_ORIGINS
      - SECRET_KEY=${SECRET_KEY:-multi-container-test-secret-key-12345}
      - DATABASE_URL=${DATABASE_URL:-postgres://signalsd-dev:@host.docker.internal:15432/signalsd_admin?sslmode=disable}
      - RATE_LIMIT_RPS=0
      - APP_INSTANCE=admin
      - READ_TIMEOUT=300s
      - WRITE_TIMEOUT=300s
      - IDLE_TIMEOUT=300s
    working_dir: /signalsd
    ports:
      - "8085:8080"  # Expose for direct testing
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health/live"]
      interval: 5s
      timeout: 5s
      retries: 5
    command: >
      sh -c "
        cd /signalsd/app &&
        echo 'Generating sqlc files...' &&
        sqlc generate &&
        echo 'Creating swagger documentation...' &&
        swag init -g ./cmd/signalsd/main.go &&
        echo 'Running database migrations...' &&
        goose -dir sql/schema postgres \"$$DATABASE_URL\" -env=none up &&
        echo 'Starting signalsd service (live reload on code changes)...' &&
        go run -mod=readonly cmd/signalsd/main.go --mode admin
      "
    networks:
      - multi-app-network
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 512M
networks:
  multi-app-network:
    driver: bridge

volumes:
  go-modules-multi:
