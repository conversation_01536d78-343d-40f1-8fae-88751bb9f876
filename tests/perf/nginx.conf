events {
    worker_connections 4096;  # Increase to handle 600+ concurrent connections
    multi_accept on;          # Accept as many connections as possible
    use epoll;                # Use efficient connection processing method
}

http {
    # Include MIME types
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Set a global client_max_body_size (match app limit)
    client_max_body_size 250m;

    # DNS resolver for dynamic upstream resolution
    resolver 127.0.0.11 valid=30s;
    
    # Upstream for signal exchange services (3 containers)
    upstream signalsd_signals {
        # Round-robin load balancing (default)
        server signals1:8080 max_fails=3 fail_timeout=30s;
        server signals2:8080 max_fails=3 fail_timeout=30s;
        server signals3:8080 max_fails=3 fail_timeout=30s;

        # Health check configuration
        keepalive 32;
    }

    # Upstream for admin services (1 container)
    upstream signalsd_admin {
        server admin:8080 max_fails=3 fail_timeout=30s;

        # Health check configuration
        keepalive 16;
    }

    # Logging configuration
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    'upstream_addr=$upstream_addr '
                    'upstream_response_time=$upstream_response_time '
                    'request_time=$request_time';

    access_log /var/log/nginx/access.log main;
    error_log /var/log/nginx/error.log warn;

    # Basic HTTP configuration
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    types_hash_max_size 2048;

    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # Increase timeouts to prevent 502 errors
    proxy_connect_timeout 60s;      # Time to establish connection with upstream
    proxy_send_timeout 60s;         # Time to send request to upstream
    proxy_read_timeout 300s;        # Time to read response from upstream (5 minutes)
    
    # Keep connections alive
    keepalive_timeout 65s;
    keepalive_requests 100;
    
    # Main server block
    server {
        listen 80;
        server_name localhost;
        
        # Set client_max_body_size at server level (match global limit)
        client_max_body_size 250m;

        # Proxy settings for better performance
        proxy_buffering on;
        proxy_buffer_size 128k;
        proxy_buffers 4 256k;
        proxy_busy_buffers_size 256k;
        proxy_temp_file_write_size 256k;
        # Use the global timeout settings (don't override them here)
        # proxy_connect_timeout, proxy_send_timeout, proxy_read_timeout are set globally

        # Headers to pass through
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # Health check endpoint (direct nginx response)
        location /health/live {
            access_log off;
            return 200 "healthy\n";
            add_header Content-Type text/plain;
        }

        # Load balancer status endpoint
        location /nginx-status {
            access_log off;
            return 200 "nginx load balancer active\n";
            add_header Content-Type text/plain;
        }

        # Signal exchange endpoints - route to signals containers
        location ~ ^/api/isn/.*/signal_types/.*/signals {
            # Disable request buffering entirely - stream directly to upstream
            proxy_request_buffering off;

            # Increase buffer sizes for this specific endpoint
            client_body_buffer_size 50m;
            client_max_body_size 250m;

            # Disable writing to temporary files
            client_body_in_file_only off;
            client_body_in_single_buffer on;

            # Proxy to signals backend
            proxy_pass http://signalsd_signals;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

            # Add custom headers to track routing
            add_header X-Request-Buffering-Status "streaming" always;
            add_header X-Service-Type "signals" always;
        }

        # Webhook endpoints - route to signals containers
        location /api/webhooks {
            proxy_pass http://signalsd_signals;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

            add_header X-Service-Type "signals" always;
        }

        # All other requests (admin, auth, ISN management, etc.) - route to admin container
        location / {
            proxy_pass http://signalsd_admin;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;

            # Add load balancer identification header
            add_header X-Load-Balancer "nginx" always;
            add_header X-Upstream-Server $upstream_addr always;
            add_header X-Service-Type "admin" always;
        }
    }
}
