version: "2"
sql:
  - engine: "postgresql"
    schema: "sql/schema/*.sql"
    queries: "sql/queries"
    gen:
      go:
        package: "database"
        out: "internal/database"
        sql_package: "pgx/v5"
        overrides:
          - db_type: "jsonb"
            go_type: "encoding/json.RawMessage"
          - db_type: "uuid"
            go_type:
              import: "github.com/google/uuid"
              type: "UUID"
          - db_type: "uuid"
            go_type:
              import: "github.com/google/uuid"
              type: "UUID"
              pointer: true
            nullable: true
          - db_type: "timestamptz"
            go_type:
              import: "time"
              type: "Time"
          - db_type: "timestamptz"
            go_type:
              import: "time"
              type: "Time"
              pointer: true
            nullable: true
          - db_type: "pg_catalog.timestamptz"
            go_type:
              import: "time"
              type: "Time"
          - db_type: "pg_catalog.timestamptz"
            go_type:
              import: "time"
              type: "Time"
              pointer: true
            nullable: true
          - db_type: "pg_catalog.date"
            go_type:
              import: "time"
              type: "Time"
          - db_type: "pg_catalog.date"
            nullable: true
            go_type:
              import: "time"
              type: "Time"
              pointer: true
        emit_json_tags: true
        emit_pointers_for_null_types: true
        emit_exported_queries: true
        rename:
          schema_url: "SchemaURL"
          readme_url: "ReadmeURL"
          storage_connection_url: "StorageConnectionURL"