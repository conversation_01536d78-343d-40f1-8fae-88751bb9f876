// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: health.sql

package database

import (
	"context"
)

const IsDatabaseRunning = `-- name: IsDatabaseRunning :one
SELECT 1 as healthy
`

func (q *Queries) IsDatabaseRunning(ctx context.Context) (int32, error) {
	row := q.db.QueryRow(ctx, IsDatabaseRunning)
	var healthy int32
	err := row.Scan(&healthy)
	return healthy, err
}
