# build signalsd image, push to gcloud and deploy to gcloud run
# this config runs the admin and signals exchange service in two separate containers
# note that - if using google cloud run - you need to set up the full load balancer to use this config
name: CD

on:
  workflow_dispatch: # manual trigger only

env:
  # app
  DATABASE_URL: ${{ secrets.DATABASE_URL }}
  SECRET_KEY: ${{ secrets.SECRET_KEY }}
  ENVIRONMENT: prod
  LOG_LEVEL: debug
  # gcloud
  ADMIN_SERVICE_NAME: signalsd-admin
  SIGNALS_SERVICE_NAME: signalsd-signals
  PROJECT_ID: signals-462015
  REGION: europe-west2
  REPOSITORY: signalsd
  IMAGE_NAME: signalsd
  DEPLOY_REGION: europe-west1
  RUNTIME_SERVICE_ACCOUNT: <EMAIL>
  GCP_CREDENTIALS: ${{ secrets.GCP_CREDENTIALS }}
  ADMIN_MAX_INSTANCES: 2
  SIGNALS_MAX_INSTANCES: 4
  ADMIN_CPU: 0.5
  SIGNALS_CPU: 1

jobs:
  tests:
    name: Deploy
    runs-on: ubuntu-latest

    steps:
      - name: Check out code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Fetch all history including tags

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: "1.24.2"
          cache: true                                
          cache-dependency-path: app/go.sum

      - name: Authenticate to Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ env.GCP_CREDENTIALS }}

      - name: Set up Cloud SDK
        uses: google-github-actions/setup-gcloud@v2

      - name: Configure Docker to use gcloud as credential helper
        run: gcloud auth configure-docker ${{ env.REGION }}-docker.pkg.dev

      - name: Build and push Docker image
        run: |
          # Get version info for Docker build
          VERSION=$(git describe --tags --abbrev=0 2>/dev/null || git describe --tags --always --dirty)
          BUILD_DATE=$(date -u +"%Y-%m-%dT%H:%M:%SZ")
          GIT_COMMIT=$(git rev-parse --short HEAD)

          docker buildx build \
          --platform linux/amd64 \
          -f app/Dockerfile \
          --build-arg VERSION=${VERSION} \
          --build-arg BUILD_DATE=${BUILD_DATE} \
          --build-arg GIT_COMMIT=${GIT_COMMIT} \
          --push \
          -t ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/${{ env.IMAGE_NAME }}:${{ github.sha }} \
          -t ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/${{ env.IMAGE_NAME }}:latest \
          .
      
      - name: Install goose
        run: go install github.com/pressly/goose/v3/cmd/goose@latest

      - name: Run migrations
        run: |
          cd app
          goose -dir sql/schema postgres ${{ env.DATABASE_URL }} up

      - name: Deploy Admin Service to Cloud Run
        run: |
          gcloud run deploy ${{ env.ADMIN_SERVICE_NAME }} \
            --image ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/${{ env.IMAGE_NAME }}:${{ github.sha }} \
            --region ${{ env.DEPLOY_REGION }} \
            --platform managed \
            --allow-unauthenticated \
            --service-account ${{ env.RUNTIME_SERVICE_ACCOUNT }} \
            --set-env-vars "DATABASE_URL=${{ env.DATABASE_URL }},SECRET_KEY=${{ env.SECRET_KEY }},ENVIRONMENT=${{ env.ENVIRONMENT }}, LOG_LEVEL=${{ env.LOG_LEVEL }}" \
            --args="--mode,admin" \
            --port 8080 \
            --memory 256Mi \
            --cpu ${{ env.ADMIN_CPU }} \
            --min-instances 1 \
            --max-instances ${{ env.ADMIN_MAX_INSTANCES }}

      - name: Deploy Signals Service to Cloud Run
        run: |
          gcloud run deploy ${{ env.SIGNALS_SERVICE_NAME }} \
            --image ${{ env.REGION }}-docker.pkg.dev/${{ env.PROJECT_ID }}/${{ env.REPOSITORY }}/${{ env.IMAGE_NAME }}:${{ github.sha }} \
            --region ${{ env.DEPLOY_REGION }} \
            --platform managed \
            --allow-unauthenticated \
            --service-account ${{ env.RUNTIME_SERVICE_ACCOUNT }} \
            --set-env-vars "DATABASE_URL=${{ env.DATABASE_URL }},SECRET_KEY=${{ env.SECRET_KEY }},ENVIRONMENT=${{ env.ENVIRONMENT }}, LOG_LEVEL=${{ env.LOG_LEVEL }}" \
            --args="--mode,signals" \
            --port 8080 \
            --memory 512Mi \
            --cpu ${{ env.SIGNALS_CPU }} \
            --min-instances 0 \
            --max-instances ${{ env.SIGNALS_MAX_INSTANCES }}
