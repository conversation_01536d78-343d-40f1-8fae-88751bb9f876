name: CI

on:
  pull_request:
    branches: [main]
  push:
    branches: [main]
  schedule:
    - cron: '0 3 * * 1'  # Weekly scan on mondays

  workflow_dispatch:

permissions:
  security-events: write
  actions: read
  contents: read  

jobs:
  tests:
    name: Tests
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:17-alpine
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_USER: postgres
          POSTGRES_DB: postgres
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

    defaults:
      run:
        working-directory: ./app

    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: "1.24.2"
          cache: true                                
          cache-dependency-path: app/go.sum    

      - name: Download Go modules
        run: go mod download

      - name: Run unit tests
        run: go test ./... || exit 1

      - name: Run integration tests
        run: go test -v -tags=integration ./test/integration/ || exit 1

  formatting:
    name: Formatting and linting
    runs-on: ubuntu-latest

    defaults:
      run:
        working-directory: ./app

    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: "1.24.2"
          cache: true                                
          cache-dependency-path: app/go.sum    

      - name: Download Go modules
        run: go mod download

      - name: Check formatting
        run: go fmt ./... || exit 1

      - name: Run vet
        run: go vet ./... || exit 1

      - name: Install staticcheck
        run: go install honnef.co/go/tools/cmd/staticcheck@latest

      - name: Run staticcheck
        run: staticcheck ./... || exit 1

  security:
    name: Security analysis
    runs-on: ubuntu-latest

    defaults:
      run:
        working-directory: ./app

    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: "1.24.2"
          cache: true                                
          cache-dependency-path: app/go.sum    

      - name: Download Go modules
        run: go mod download

      - name: Install gosec
        run: go install github.com/securego/gosec/v2/cmd/gosec@latest

      - name: Run gosec
        run: gosec -exclude-generated ./... 

      - name: Initialize CodeQL
        uses: github/codeql-action/init@v3
        with:
          languages: go

      - name: Autobuild
        uses: github/codeql-action/autobuild@v3

      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3
        if: >
          github.event_name != 'pull_request' ||
          github.event.pull_request.head.repo.full_name == github.repository




